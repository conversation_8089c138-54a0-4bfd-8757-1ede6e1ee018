#!/usr/bin/env python3
"""
EOF加权空间映射数据预处理脚本
实现策略2：使用EOF模态的空间结构对PCs进行加权映射
将加权场作为额外输入通道与SST和ERA5数据结合

作者: AI Assistant
日期: 2025-07-07
"""

import numpy as np
import xarray as xr
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']
rcParams['axes.unicode_minus'] = False
import os
from tqdm import tqdm
from scipy.interpolate import griddata

print("🚀 开始EOF加权空间映射数据预处理...")

# 创建结果文件夹
os.makedirs('eof_weighted_data', exist_ok=True)
os.makedirs('eof_weighted_figures', exist_ok=True)

# 获取脚本所在目录和项目根目录
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)

print(f"脚本目录: {script_dir}")
print(f"项目根目录: {project_root}")

# 检查必要的输入文件 - 使用绝对路径
required_files = {
    'EOFs_k.npy': os.path.join(project_root, 'results', 'EOFs_k.npy'),
    'PCs_train.npy': os.path.join(project_root, 'results', 'PCs_train.npy'),
    'PCs_val.npy': os.path.join(project_root, 'results', 'PCs_val.npy'),
    'PCs_test.npy': os.path.join(project_root, 'results', 'PCs_test.npy'),
    'X_mean.npy': os.path.join(project_root, 'results', 'X_mean.npy'),
    'sst_mean.npy': os.path.join(project_root, 'results', 'sst_mean.npy'),
    'sst_std.npy': os.path.join(project_root, 'results', 'sst_std.npy'),
    'SST-V2.nc': os.path.join(project_root, 'SST-V2.nc'),
    'data_ERA5.nc': os.path.join(project_root, 'data_ERA5.nc')
}

print("📋 检查必要文件...")
missing_files = []
for name, file_path in required_files.items():
    if os.path.exists(file_path):
        print(f"✅ {name}: {file_path}")
    else:
        print(f"❌ {name}: {file_path} - 文件不存在")
        missing_files.append(name)

if missing_files:
    print(f"\n❌ 缺少以下文件: {missing_files}")
    print("请确保已运行主项目的 preprocess_and_eof.py 生成EOF分解结果")
    exit(1)

# 加载EOF分解结果
print("\n📊 加载EOF分解结果...")
EOFs_k = np.load(required_files['EOFs_k.npy'])
PCs_train = np.load(required_files['PCs_train.npy'])
PCs_val = np.load(required_files['PCs_val.npy'])
PCs_test = np.load(required_files['PCs_test.npy'])
X_mean = np.load(required_files['X_mean.npy'])

# 加载标准化参数
sst_mean = np.load(required_files['sst_mean.npy'])
sst_std = np.load(required_files['sst_std.npy'])

print(f"EOF模态形状: {EOFs_k.shape}")
print(f"训练集PCs形状: {PCs_train.shape}")
print(f"验证集PCs形状: {PCs_val.shape}")
print(f"测试集PCs形状: {PCs_test.shape}")

# 加载原始数据获取空间维度信息
print("\n🌊 加载原始数据...")
sst_data = xr.open_dataset(required_files['SST-V2.nc'])
era5_data = xr.open_dataset(required_files['data_ERA5.nc'])

# 获取空间维度
n_lat = len(sst_data.latitude)
n_lon = len(sst_data.longitude)
n_vars = 2  # SST + T2m
k = EOFs_k.shape[1]  # EOF模态数量

print(f"空间维度: {n_lat} x {n_lon}")
print(f"变量数量: {n_vars}")
print(f"EOF模态数量: {k}")

# 重塑EOF模态为空间形式
print("\n🔧 重塑EOF模态为空间形式...")
EOFs_spatial = EOFs_k.reshape(n_vars, n_lat, n_lon, k)
EOFs_sst = EOFs_spatial[0]      # SST的EOF模态 [lat, lon, k]
EOFs_t2m = EOFs_spatial[1]      # T2m的EOF模态 [lat, lon, k]

print(f"SST EOF模态形状: {EOFs_sst.shape}")
print(f"T2m EOF模态形状: {EOFs_t2m.shape}")

def create_eof_weighted_features(pcs_sequence, eof_modes_sst, eof_modes_t2m):
    """
    创建EOF加权空间特征
    
    参数:
    - pcs_sequence: PCs时间序列 [k, time_steps]
    - eof_modes_sst: SST的EOF空间模态 [lat, lon, k]
    - eof_modes_t2m: T2m的EOF空间模态 [lat, lon, k]
    
    返回:
    - weighted_features: 加权空间特征 [time_steps, k, lat, lon]
    """
    time_steps = pcs_sequence.shape[1]
    lat, lon, k = eof_modes_sst.shape
    
    # 初始化加权特征数组
    weighted_features = np.zeros((time_steps, k, lat, lon))
    
    print(f"创建EOF加权特征，时间步数: {time_steps}")
    
    for t in tqdm(range(time_steps), desc="生成加权特征"):
        for mode_idx in range(k):
            # 获取当前时间步的PC值
            pc_value = pcs_sequence[mode_idx, t]
            
            # 使用SST的EOF模态进行加权映射
            # 这里选择SST模态是因为我们主要预测SST
            weighted_features[t, mode_idx] = pc_value * eof_modes_sst[:, :, mode_idx]
    
    return weighted_features

# 创建训练集的EOF加权特征
print("\n🎯 创建训练集EOF加权特征...")
train_weighted_features = create_eof_weighted_features(PCs_train, EOFs_sst, EOFs_t2m)

# 创建验证集的EOF加权特征  
print("\n🎯 创建验证集EOF加权特征...")
val_weighted_features = create_eof_weighted_features(PCs_val, EOFs_sst, EOFs_t2m)

# 创建测试集的EOF加权特征
print("\n🎯 创建测试集EOF加权特征...")
test_weighted_features = create_eof_weighted_features(PCs_test, EOFs_sst, EOFs_t2m)

print(f"训练集加权特征形状: {train_weighted_features.shape}")
print(f"验证集加权特征形状: {val_weighted_features.shape}")
print(f"测试集加权特征形状: {test_weighted_features.shape}")

# 保存EOF加权特征
print("\n💾 保存EOF加权特征...")
np.save('eof_weighted_data/train_eof_weighted_features.npy', train_weighted_features)
np.save('eof_weighted_data/val_eof_weighted_features.npy', val_weighted_features)
np.save('eof_weighted_data/test_eof_weighted_features.npy', test_weighted_features)

# 保存EOF空间模态（用于后续分析）
np.save('eof_weighted_data/EOFs_sst_spatial.npy', EOFs_sst)
np.save('eof_weighted_data/EOFs_t2m_spatial.npy', EOFs_t2m)

# 现在处理ERA5数据，与之前的预处理保持一致
print("\n🌍 处理ERA5数据...")

# 重命名时间维度
era5_data = era5_data.rename({'valid_time': 'time'})

def interpolate_era5_to_sst_grid(era5_ds, sst_ds, var_name):
    """将ERA5变量插值到SST网格"""
    print(f"插值 {var_name} 到SST网格...")
    
    # 创建原始经纬度网格点
    lon_era5, lat_era5 = np.meshgrid(era5_ds.longitude.values, era5_ds.latitude.values)
    
    # 创建目标经纬度网格点
    lon_sst, lat_sst = np.meshgrid(sst_ds.longitude.values, sst_ds.latitude.values)
    
    # 初始化结果数组
    result = np.zeros((len(era5_ds.time), len(sst_ds.latitude), len(sst_ds.longitude)))
    
    # 对每个时间点进行插值
    for t in tqdm(range(len(era5_ds.time)), desc=f"插值{var_name}"):
        # 获取ERA5当前时间的数据
        z = era5_ds[var_name].isel(time=t).values
        
        # 将原始经纬度网格和数据平铺
        points = np.column_stack((lon_era5.flatten(), lat_era5.flatten()))
        values = z.flatten()
        
        # 使用griddata进行插值
        result[t] = griddata(points, values, (lon_sst, lat_sst), method='linear')
    
    return result

# 插值ERA5变量
era5_vars = ['u10', 'v10', 't2m', 'msl']
era5_interpolated = {}

for var in era5_vars:
    if var in era5_data.data_vars:
        era5_interpolated[var] = interpolate_era5_to_sst_grid(era5_data, sst_data, var)

# 获取SST数据
sst_values = sst_data.analysed_sst.values

# 数据集划分（与EOF分解保持一致：7:2:1）
n_time_total = sst_values.shape[0]
train_end = int(n_time_total * 0.7)
val_end = int(n_time_total * 0.9)

print(f"\n📊 数据集划分:")
print(f"  训练集: 0 - {train_end} ({train_end} 个时间点)")
print(f"  验证集: {train_end} - {val_end} ({val_end - train_end} 个时间点)")
print(f"  测试集: {val_end} - {n_time_total} ({n_time_total - val_end} 个时间点)")

# 划分SST数据集
sst_train = sst_values[:train_end]
sst_val = sst_values[train_end:val_end]
sst_test = sst_values[val_end:]

# 划分ERA5数据集
era5_train = {}
era5_val = {}
era5_test = {}

for var, data in era5_interpolated.items():
    era5_train[var] = data[:train_end]
    era5_val[var] = data[train_end:val_end]
    era5_test[var] = data[val_end:]

# 标准化处理
print("\n📏 标准化处理...")

# SST标准化（使用已有的标准化参数）
sst_train_norm = (sst_train - sst_mean) / sst_std
sst_val_norm = (sst_val - sst_mean) / sst_std
sst_test_norm = (sst_test - sst_mean) / sst_std

# ERA5变量标准化
era5_stats = {}
era5_train_norm = {}
era5_val_norm = {}
era5_test_norm = {}

for var in era5_vars:
    if var in era5_interpolated:
        # 计算训练集统计量
        era5_stats[var] = {
            'mean': np.mean(era5_train[var]),
            'std': np.std(era5_train[var])
        }
        
        # 标准化
        era5_train_norm[var] = (era5_train[var] - era5_stats[var]['mean']) / era5_stats[var]['std']
        era5_val_norm[var] = (era5_val[var] - era5_stats[var]['mean']) / era5_stats[var]['std']
        era5_test_norm[var] = (era5_test[var] - era5_stats[var]['mean']) / era5_stats[var]['std']

# 保存标准化数据
print("\n💾 保存标准化数据...")

# 保存SST数据
np.save('eof_weighted_data/sst_train_norm.npy', sst_train_norm)
np.save('eof_weighted_data/sst_val_norm.npy', sst_val_norm)
np.save('eof_weighted_data/sst_test_norm.npy', sst_test_norm)

# 保存ERA5数据
for var in era5_vars:
    if var in era5_train_norm:
        np.save(f'eof_weighted_data/{var}_train_norm.npy', era5_train_norm[var])
        np.save(f'eof_weighted_data/{var}_val_norm.npy', era5_val_norm[var])
        np.save(f'eof_weighted_data/{var}_test_norm.npy', era5_test_norm[var])

# 保存标准化参数
np.save('eof_weighted_data/era5_stats.npy', era5_stats)

print("\n✅ EOF加权空间映射数据预处理完成！")
print("\n📁 生成的文件:")
print("  - train/val/test_eof_weighted_features.npy: EOF加权空间特征")
print("  - sst_train/val/test_norm.npy: 标准化SST数据")
print("  - {var}_train/val/test_norm.npy: 标准化ERA5数据")
print("  - EOFs_sst/t2m_spatial.npy: 空间形式的EOF模态")
print("  - era5_stats.npy: ERA5标准化参数")

# 关闭数据文件
sst_data.close()
era5_data.close()

print("\n🎉 数据预处理完成，可以开始训练EOF加权映射ConvLSTM模型！")
