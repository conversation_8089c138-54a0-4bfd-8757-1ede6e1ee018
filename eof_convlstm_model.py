import torch
import torch.nn as nn
import numpy as np
import torch.nn.functional as F

# ConvLSTM Cell implementation
class ConvLSTMCell(nn.Module):
    def __init__(self, input_dim, hidden_dim, kernel_size, bias=True):
        super(ConvLSTMCell, self).__init__()
        
        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.padding = kernel_size[0] // 2, kernel_size[1] // 2
        self.bias = bias
        
        self.conv = nn.Conv2d(in_channels=self.input_dim + self.hidden_dim,
                              out_channels=4 * self.hidden_dim,
                              kernel_size=self.kernel_size,
                              padding=self.padding,
                              bias=self.bias)

    def forward(self, input_tensor, cur_state):
        h_cur, c_cur = cur_state
        
        combined = torch.cat([input_tensor, h_cur], dim=1)
        combined_conv = self.conv(combined)
        
        cc_i, cc_f, cc_o, cc_g = torch.split(combined_conv, self.hidden_dim, dim=1)
        i = torch.sigmoid(cc_i)
        f = torch.sigmoid(cc_f)
        o = torch.sigmoid(cc_o)
        g = torch.tanh(cc_g)

        c_next = f * c_cur + i * g
        h_next = o * torch.tanh(c_next)
        
        return h_next, c_next

    def init_hidden(self, batch_size, image_size):
        height, width = image_size
        return (torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device),
                torch.zeros(batch_size, self.hidden_dim, height, width, device=self.conv.weight.device))


class ConvLSTM(nn.Module):
    def __init__(self, input_dim, hidden_dim, kernel_size, num_layers, batch_first=True, bias=True, return_all_layers=False):
        super(ConvLSTM, self).__init__()

        self._check_kernel_size_consistency(kernel_size)

        kernel_size = self._extend_for_multilayer(kernel_size, num_layers)
        hidden_dim = self._extend_for_multilayer(hidden_dim, num_layers)
        if not len(kernel_size) == len(hidden_dim) == num_layers:
            raise ValueError('Inconsistent list length.')

        self.input_dim = input_dim
        self.hidden_dim = hidden_dim
        self.kernel_size = kernel_size
        self.num_layers = num_layers
        self.batch_first = batch_first
        self.bias = bias
        self.return_all_layers = return_all_layers

        cell_list = []
        for i in range(0, self.num_layers):
            cur_input_dim = self.input_dim if i == 0 else self.hidden_dim[i - 1]
            cell_list.append(ConvLSTMCell(input_dim=cur_input_dim,
                                          hidden_dim=self.hidden_dim[i],
                                          kernel_size=self.kernel_size[i],
                                          bias=self.bias))

        self.cell_list = nn.ModuleList(cell_list)

    def forward(self, input_tensor, hidden_state=None):
        if not self.batch_first:
            input_tensor = input_tensor.permute(1, 0, 2, 3, 4)

        b, seq_len, _, h, w = input_tensor.size()

        if hidden_state is not None:
            raise NotImplementedError()
        else:
            hidden_state = self._init_hidden(batch_size=b, image_size=(h, w))

        layer_output_list = []
        last_state_list = []

        cur_layer_input = input_tensor

        for layer_idx in range(self.num_layers):
            h, c = hidden_state[layer_idx]
            output_inner = []
            for t in range(seq_len):
                h, c = self.cell_list[layer_idx](input_tensor=cur_layer_input[:, t, :, :, :], cur_state=[h, c])
                output_inner.append(h)

            layer_output = torch.stack(output_inner, dim=1)
            cur_layer_input = layer_output

            layer_output_list.append(layer_output)
            last_state_list.append([h, c])

        if not self.return_all_layers:
            layer_output_list = layer_output_list[-1:]
            last_state_list = last_state_list[-1:]

        return layer_output_list, last_state_list

    def _init_hidden(self, batch_size, image_size):
        init_states = []
        for i in range(self.num_layers):
            init_states.append(self.cell_list[i].init_hidden(batch_size, image_size))
        return init_states

    @staticmethod
    def _check_kernel_size_consistency(kernel_size):
        if not (isinstance(kernel_size, tuple) or
                (isinstance(kernel_size, list) and all([isinstance(elem, tuple) for elem in kernel_size]))):
            raise ValueError('`kernel_size` must be tuple or list of tuples')

    @staticmethod
    def _extend_for_multilayer(param, num_layers):
        if not isinstance(param, list):
            param = [param] * num_layers
        return param


# EOF-based ConvLSTM Model for SST Prediction
class EOFConvLSTMModel(nn.Module):
    def __init__(self, num_pcs, seq_len, pred_len, hidden_dim=64, kernel_size=(3, 3), num_layers=2, 
                 spatial_size=(8, 8), dropout=0.1):
        super(EOFConvLSTMModel, self).__init__()
        
        self.num_pcs = num_pcs
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.spatial_size = spatial_size
        
        # PC to spatial mapping layer
        self.pc_to_spatial = nn.Sequential(
            nn.Linear(num_pcs, spatial_size[0] * spatial_size[1] * 16),
            nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # ConvLSTM layers
        self.convlstm = ConvLSTM(
            input_dim=16,
            hidden_dim=[hidden_dim, hidden_dim],
            kernel_size=kernel_size,
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )
        
        # Spatial to PC mapping layer
        self.spatial_to_pc = nn.Sequential(
            nn.Conv2d(hidden_dim, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(32, num_pcs),
            nn.Dropout(dropout)
        )
        
        # Residual connection
        self.residual_proj = nn.Linear(num_pcs, num_pcs)
        
    def forward(self, x):
        # x: [batch_size, seq_len, num_pcs]
        batch_size, seq_len, num_pcs = x.size()
        
        # Convert PCs to spatial representation
        spatial_inputs = []
        for t in range(seq_len):
            pc_t = x[:, t, :]  # [batch_size, num_pcs]
            spatial_t = self.pc_to_spatial(pc_t)  # [batch_size, spatial_size[0]*spatial_size[1]*16]
            spatial_t = spatial_t.view(batch_size, 16, self.spatial_size[0], self.spatial_size[1])
            spatial_inputs.append(spatial_t)
        
        spatial_sequence = torch.stack(spatial_inputs, dim=1)  # [batch_size, seq_len, 16, H, W]
        
        # ConvLSTM forward
        layer_output_list, last_state_list = self.convlstm(spatial_sequence)
        convlstm_output = layer_output_list[0]  # [batch_size, seq_len, hidden_dim, H, W]
        
        # Use last time step for prediction
        last_output = convlstm_output[:, -1, :, :, :]  # [batch_size, hidden_dim, H, W]
        
        # Convert back to PCs
        predicted_pcs = self.spatial_to_pc(last_output)  # [batch_size, num_pcs]
        
        # Add residual connection
        residual = self.residual_proj(x[:, -1, :])  # [batch_size, num_pcs]
        output = predicted_pcs + residual
        
        # Reshape for pred_len=1
        output = output.unsqueeze(1)  # [batch_size, 1, num_pcs]
        
        return output


# SST+PCs ConvLSTM Model for direct SST prediction
class SSTWithPCsConvLSTMModel(nn.Module):
    def __init__(self, num_pcs, seq_len, pred_len, hidden_dim=64, kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(SSTWithPCsConvLSTMModel, self).__init__()

        self.num_pcs = num_pcs
        self.seq_len = seq_len
        self.pred_len = pred_len

        # 输入通道数: 1(SST) + num_pcs(PCs)
        input_channels = 1 + num_pcs

        # ConvLSTM layers
        self.convlstm = ConvLSTM(
            input_dim=input_channels,
            hidden_dim=[hidden_dim, hidden_dim],
            kernel_size=kernel_size,
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # Output projection to SST
        self.output_proj = nn.Sequential(
            nn.Conv2d(hidden_dim, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv2d(32, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(16, 1, kernel_size=3, padding=1)  # 输出单通道SST
        )

        # Residual connection for SST
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1)

    def forward(self, x):
        # x: [batch_size, seq_len, 1+num_pcs, lat, lon]
        batch_size, seq_len, channels, lat, lon = x.size()

        # ConvLSTM forward
        layer_output_list, last_state_list = self.convlstm(x)
        convlstm_output = layer_output_list[0]  # [batch_size, seq_len, hidden_dim, lat, lon]

        # Use last time step for prediction
        last_output = convlstm_output[:, -1, :, :, :]  # [batch_size, hidden_dim, lat, lon]

        # Project to SST
        predicted_sst = self.output_proj(last_output)  # [batch_size, 1, lat, lon]

        # Add residual connection (use last SST input)
        last_sst = x[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon] - 只取SST通道
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst

        # Reshape for pred_len=1: [batch_size, pred_len, lat, lon]
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]

        return output_sst


# Alternative: Multi-scale SST+PCs ConvLSTM Model
class MultiScaleSSTWithPCsConvLSTMModel(nn.Module):
    def __init__(self, num_pcs, seq_len, pred_len, hidden_dim=64, kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(MultiScaleSSTWithPCsConvLSTMModel, self).__init__()

        self.num_pcs = num_pcs
        self.seq_len = seq_len
        self.pred_len = pred_len

        # 输入通道数: 1(SST) + num_pcs(PCs)
        input_channels = 1 + num_pcs

        # Multi-scale ConvLSTM layers
        self.convlstm_fine = ConvLSTM(
            input_dim=input_channels,
            hidden_dim=[hidden_dim//2, hidden_dim//2],
            kernel_size=(3, 3),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        self.convlstm_coarse = ConvLSTM(
            input_dim=input_channels,
            hidden_dim=[hidden_dim//2, hidden_dim//2],
            kernel_size=(5, 5),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # Feature fusion
        self.fusion = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Dropout(dropout)
        )

        # Output projection to SST
        self.output_proj = nn.Sequential(
            nn.Conv2d(hidden_dim, 32, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Conv2d(32, 16, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv2d(16, 1, kernel_size=3, padding=1)
        )

        # Residual connection
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1)

    def forward(self, x):
        # x: [batch_size, seq_len, 1+num_pcs, lat, lon]
        batch_size, seq_len, channels, lat, lon = x.size()

        # Multi-scale ConvLSTM forward
        fine_output_list, _ = self.convlstm_fine(x)
        coarse_output_list, _ = self.convlstm_coarse(x)

        fine_output = fine_output_list[0][:, -1, :, :, :]  # [batch_size, hidden_dim//2, lat, lon]
        coarse_output = coarse_output_list[0][:, -1, :, :, :]  # [batch_size, hidden_dim//2, lat, lon]

        # Concatenate multi-scale features
        fused_features = torch.cat([fine_output, coarse_output], dim=1)  # [batch_size, hidden_dim, lat, lon]
        fused_features = self.fusion(fused_features)

        # Project to SST
        predicted_sst = self.output_proj(fused_features)  # [batch_size, 1, lat, lon]

        # Add residual connection
        last_sst = x[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst

        # Reshape for pred_len=1
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]

        return output_sst


class SSTWithPCsAndERA5ConvLSTMModel(nn.Module):
    """
    SST+PCs+ERA5 ConvLSTM模型
    将SST场、PCs特征和ERA5大气变量作为输入，直接预测SST场

    输入: [SST, PCs_spatial, u10, v10, t2m, msl] -> [batch_size, seq_len, 1+num_pcs+4, lat, lon]
    输出: 预测的SST场 [batch_size, pred_len, lat, lon]
    """

    def __init__(self, num_pcs=10, seq_len=14, pred_len=1, hidden_dim=64,
                 kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(SSTWithPCsAndERA5ConvLSTMModel, self).__init__()

        self.num_pcs = num_pcs
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim

        # 输入通道数: 1(SST) + num_pcs + 4(ERA5变量)
        input_dim = 1 + num_pcs + 4

        # ConvLSTM层
        self.convlstm = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=kernel_size,
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 特征融合层 - 在ConvLSTM之前进行特征预处理
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(input_dim, input_dim, kernel_size=1, padding=0),
            nn.BatchNorm2d(input_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )

        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim // 2, hidden_dim // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 4, 1, kernel_size=1, padding=0)
        )

        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)

        # 注意力机制 - 对不同特征通道加权
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(input_dim, input_dim // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(input_dim // 4, input_dim, kernel_size=1),
            nn.Sigmoid()
        )

    def forward(self, x):
        # x: [batch_size, seq_len, 1+num_pcs+4, lat, lon]
        batch_size, seq_len, channels, lat, lon = x.size()

        # 对每个时间步应用特征融合和注意力
        processed_sequence = []
        for t in range(seq_len):
            x_t = x[:, t]  # [batch_size, channels, lat, lon]

            # 通道注意力
            attention_weights = self.channel_attention(x_t)
            x_t_attended = x_t * attention_weights

            # 特征融合
            x_t_fused = self.feature_fusion(x_t_attended)
            processed_sequence.append(x_t_fused)

        # 重新组合时间序列
        processed_x = torch.stack(processed_sequence, dim=1)  # [batch_size, seq_len, channels, lat, lon]

        # ConvLSTM forward
        layer_output_list, last_state_list = self.convlstm(processed_x)
        convlstm_output = layer_output_list[0]  # [batch_size, seq_len, hidden_dim, lat, lon]

        # 使用最后一个时间步进行预测
        last_output = convlstm_output[:, -1]  # [batch_size, hidden_dim, lat, lon]

        # 输出投影
        predicted_sst = self.output_projection(last_output)  # [batch_size, 1, lat, lon]

        # 残差连接 - 与输入的最后一个时间步的SST相加
        last_sst = x[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst

        # Reshape for pred_len=1
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]

        return output_sst


class MultiScaleSSTWithPCsAndERA5ConvLSTMModel(nn.Module):
    """
    多尺度SST+PCs+ERA5 ConvLSTM模型
    使用多个不同尺度的ConvLSTM分支处理输入特征，然后融合预测结果

    输入: [SST, PCs_spatial, u10, v10, t2m, msl] -> [batch_size, seq_len, 1+num_pcs+4, lat, lon]
    输出: 预测的SST场 [batch_size, pred_len, lat, lon]
    """

    def __init__(self, num_pcs=10, seq_len=14, pred_len=1, hidden_dim=64,
                 kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(MultiScaleSSTWithPCsAndERA5ConvLSTMModel, self).__init__()

        self.num_pcs = num_pcs
        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim

        # 输入通道数: 1(SST) + num_pcs + 4(ERA5变量)
        input_dim = 1 + num_pcs + 4

        # 多尺度ConvLSTM分支
        # 细尺度分支 (3x3卷积核)
        self.convlstm_fine = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(3, 3),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 中尺度分支 (5x5卷积核)
        self.convlstm_medium = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(5, 5),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 粗尺度分支 (7x7卷积核)
        self.convlstm_coarse = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(7, 7),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(input_dim, input_dim, kernel_size=1, padding=0),
            nn.BatchNorm2d(input_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )

        # 多尺度特征融合
        fusion_dim = hidden_dim * 3  # 三个分支的特征拼接
        self.multiscale_fusion = nn.Sequential(
            nn.Conv2d(fusion_dim, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True)
        )

        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim // 2, hidden_dim // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim // 4, 1, kernel_size=1, padding=0)
        )

        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)

        # 通道注意力机制
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(input_dim, input_dim // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(input_dim // 4, input_dim, kernel_size=1),
            nn.Sigmoid()
        )

        # 多尺度注意力权重
        self.scale_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(fusion_dim, fusion_dim // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(fusion_dim // 4, 3, kernel_size=1),  # 3个尺度的权重
            nn.Softmax(dim=1)
        )

    def forward(self, x):
        # x: [batch_size, seq_len, 1+num_pcs+4, lat, lon]
        batch_size, seq_len, channels, lat, lon = x.size()

        # 对每个时间步应用特征融合和注意力
        processed_sequence = []
        for t in range(seq_len):
            x_t = x[:, t]  # [batch_size, channels, lat, lon]

            # 通道注意力
            attention_weights = self.channel_attention(x_t)
            x_t_attended = x_t * attention_weights

            # 特征融合
            x_t_fused = self.feature_fusion(x_t_attended)
            processed_sequence.append(x_t_fused)

        # 重新组合时间序列
        processed_x = torch.stack(processed_sequence, dim=1)  # [batch_size, seq_len, channels, lat, lon]

        # 多尺度ConvLSTM前向传播
        fine_output, _ = self.convlstm_fine(processed_x)
        medium_output, _ = self.convlstm_medium(processed_x)
        coarse_output, _ = self.convlstm_coarse(processed_x)

        # 获取最后时间步的输出
        fine_last = fine_output[0][:, -1]      # [batch_size, hidden_dim, lat, lon]
        medium_last = medium_output[0][:, -1]  # [batch_size, hidden_dim, lat, lon]
        coarse_last = coarse_output[0][:, -1]  # [batch_size, hidden_dim, lat, lon]

        # 拼接多尺度特征
        multiscale_features = torch.cat([fine_last, medium_last, coarse_last], dim=1)
        # [batch_size, hidden_dim*3, lat, lon]

        # 计算尺度注意力权重
        scale_weights = self.scale_attention(multiscale_features)  # [batch_size, 3, 1, 1]

        # 应用注意力权重
        weighted_fine = fine_last * scale_weights[:, 0:1]
        weighted_medium = medium_last * scale_weights[:, 1:2]
        weighted_coarse = coarse_last * scale_weights[:, 2:3]

        # 重新拼接加权特征
        weighted_features = torch.cat([weighted_fine, weighted_medium, weighted_coarse], dim=1)

        # 多尺度特征融合
        fused_features = self.multiscale_fusion(weighted_features)

        # 输出投影
        predicted_sst = self.output_projection(fused_features)  # [batch_size, 1, lat, lon]

        # 残差连接
        last_sst = x[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst

        # Reshape for pred_len=1
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]

        return output_sst


class SSTWithERA5ConvLSTMModel(nn.Module):
    """
    SST+ERA5 ConvLSTM消融实验模型
    只使用SST场和ERA5大气变量作为输入，移除PCs特征
    用于量化PCs特征对预测性能的贡献

    输入: [SST, u10, v10, t2m, msl] -> [batch_size, seq_len, 1+4, lat, lon]
    输出: 预测的SST场 [batch_size, pred_len, lat, lon]
    """

    def __init__(self, seq_len=14, pred_len=1, hidden_dim=64,
                 kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(SSTWithERA5ConvLSTMModel, self).__init__()

        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim

        # 输入通道数: 1(SST) + 4(ERA5变量)，移除PCs
        input_dim = 1 + 4

        # ConvLSTM层
        self.convlstm = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=kernel_size,
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(input_dim, input_dim, kernel_size=1, padding=0),
            nn.BatchNorm2d(input_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )

        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim // 2, hidden_dim // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Conv2d(hidden_dim // 4, 1, kernel_size=1, padding=0)
        )

        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)

        # 通道注意力机制 - 只针对SST+ERA5特征
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(input_dim, input_dim // 2, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(input_dim // 2, input_dim, kernel_size=1),
            nn.Sigmoid()
        )

    def forward(self, x):
        # x: [batch_size, seq_len, 1+4, lat, lon] (移除了PCs维度)
        batch_size, seq_len, channels, lat, lon = x.size()

        # 对每个时间步应用特征融合和注意力
        processed_sequence = []
        for t in range(seq_len):
            x_t = x[:, t]  # [batch_size, channels, lat, lon]

            # 通道注意力
            attention_weights = self.channel_attention(x_t)
            x_t_attended = x_t * attention_weights

            # 特征融合
            x_t_fused = self.feature_fusion(x_t_attended)
            processed_sequence.append(x_t_fused)

        # 重新组合时间序列
        processed_x = torch.stack(processed_sequence, dim=1)  # [batch_size, seq_len, channels, lat, lon]

        # ConvLSTM forward
        layer_output_list, last_state_list = self.convlstm(processed_x)
        convlstm_output = layer_output_list[0]  # [batch_size, seq_len, hidden_dim, lat, lon]

        # 使用最后一个时间步进行预测
        last_output = convlstm_output[:, -1]  # [batch_size, hidden_dim, lat, lon]

        # 输出投影
        predicted_sst = self.output_projection(last_output)  # [batch_size, 1, lat, lon]

        # 残差连接 - 与输入的最后一个时间步的SST相加
        last_sst = x[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst

        # Reshape for pred_len=1
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]

        return output_sst


class MultiScaleSSTWithERA5ConvLSTMModel(nn.Module):
    """
    多尺度SST+ERA5 ConvLSTM消融实验模型
    多尺度版本的消融实验模型，移除PCs特征
    """

    def __init__(self, seq_len=14, pred_len=1, hidden_dim=64,
                 kernel_size=(3, 3), num_layers=2, dropout=0.1):
        super(MultiScaleSSTWithERA5ConvLSTMModel, self).__init__()

        self.seq_len = seq_len
        self.pred_len = pred_len
        self.hidden_dim = hidden_dim

        # 输入通道数: 1(SST) + 4(ERA5变量)，移除PCs
        input_dim = 1 + 4

        # 多尺度ConvLSTM分支
        # 细尺度分支 (3x3卷积核)
        self.convlstm_fine = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(3, 3),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 中尺度分支 (5x5卷积核)
        self.convlstm_medium = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(5, 5),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 粗尺度分支 (7x7卷积核)
        self.convlstm_coarse = ConvLSTM(
            input_dim=input_dim,
            hidden_dim=hidden_dim,
            kernel_size=(7, 7),
            num_layers=num_layers,
            batch_first=True,
            bias=True,
            return_all_layers=False
        )

        # 特征融合层
        self.feature_fusion = nn.Sequential(
            nn.Conv2d(input_dim, input_dim, kernel_size=1, padding=0),
            nn.BatchNorm2d(input_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout)
        )

        # 多尺度特征融合
        fusion_dim = hidden_dim * 3  # 三个分支的特征拼接
        self.multiscale_fusion = nn.Sequential(
            nn.Conv2d(fusion_dim, hidden_dim, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim, hidden_dim // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 2),
            nn.ReLU(inplace=True)
        )

        # 输出投影层
        self.output_projection = nn.Sequential(
            nn.Conv2d(hidden_dim // 2, hidden_dim // 4, kernel_size=3, padding=1),
            nn.BatchNorm2d(hidden_dim // 4),
            nn.ReLU(inplace=True),
            nn.Dropout2d(dropout),
            nn.Conv2d(hidden_dim // 4, 1, kernel_size=1, padding=0)
        )

        # 残差连接
        self.residual_proj = nn.Conv2d(1, 1, kernel_size=1, padding=0)

        # 通道注意力机制
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(input_dim, input_dim // 2, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(input_dim // 2, input_dim, kernel_size=1),
            nn.Sigmoid()
        )

        # 多尺度注意力权重
        self.scale_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(fusion_dim, fusion_dim // 4, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(fusion_dim // 4, 3, kernel_size=1),  # 3个尺度的权重
            nn.Softmax(dim=1)
        )

    def forward(self, x):
        # x: [batch_size, seq_len, 1+4, lat, lon] (移除了PCs维度)
        batch_size, seq_len, channels, lat, lon = x.size()

        # 对每个时间步应用特征融合和注意力
        processed_sequence = []
        for t in range(seq_len):
            x_t = x[:, t]  # [batch_size, channels, lat, lon]

            # 通道注意力
            attention_weights = self.channel_attention(x_t)
            x_t_attended = x_t * attention_weights

            # 特征融合
            x_t_fused = self.feature_fusion(x_t_attended)
            processed_sequence.append(x_t_fused)

        # 重新组合时间序列
        processed_x = torch.stack(processed_sequence, dim=1)  # [batch_size, seq_len, channels, lat, lon]

        # 多尺度ConvLSTM前向传播
        fine_output, _ = self.convlstm_fine(processed_x)
        medium_output, _ = self.convlstm_medium(processed_x)
        coarse_output, _ = self.convlstm_coarse(processed_x)

        # 获取最后时间步的输出
        fine_last = fine_output[0][:, -1]      # [batch_size, hidden_dim, lat, lon]
        medium_last = medium_output[0][:, -1]  # [batch_size, hidden_dim, lat, lon]
        coarse_last = coarse_output[0][:, -1]  # [batch_size, hidden_dim, lat, lon]

        # 拼接多尺度特征
        multiscale_features = torch.cat([fine_last, medium_last, coarse_last], dim=1)
        # [batch_size, hidden_dim*3, lat, lon]

        # 计算尺度注意力权重
        scale_weights = self.scale_attention(multiscale_features)  # [batch_size, 3, 1, 1]

        # 应用注意力权重
        weighted_fine = fine_last * scale_weights[:, 0:1]
        weighted_medium = medium_last * scale_weights[:, 1:2]
        weighted_coarse = coarse_last * scale_weights[:, 2:3]

        # 重新拼接加权特征
        weighted_features = torch.cat([weighted_fine, weighted_medium, weighted_coarse], dim=1)

        # 多尺度特征融合
        fused_features = self.multiscale_fusion(weighted_features)

        # 输出投影
        predicted_sst = self.output_projection(fused_features)  # [batch_size, 1, lat, lon]

        # 残差连接
        last_sst = x[:, -1, 0:1, :, :]  # [batch_size, 1, lat, lon]
        residual_sst = self.residual_proj(last_sst)
        output_sst = predicted_sst + residual_sst

        # Reshape for pred_len=1
        output_sst = output_sst.squeeze(1)  # [batch_size, lat, lon]
        if self.pred_len == 1:
            output_sst = output_sst.unsqueeze(1)  # [batch_size, 1, lat, lon]

        return output_sst
