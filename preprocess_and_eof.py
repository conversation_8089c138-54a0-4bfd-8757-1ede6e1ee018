import xarray as xr
import numpy as np
import matplotlib.pyplot as plt
from scipy import linalg
from scipy.interpolate import griddata
import pandas as pd
from sklearn.preprocessing import StandardScaler
import os

print("开始数据预处理和EOF分解...")

# 创建结果文件夹
os.makedirs('results', exist_ok=True)
os.makedirs('figures', exist_ok=True)

# 第一步：数据准备与预处理
print("第一步：数据准备与预处理")

# 读取数据
print("读取SST数据...")
sst_data = xr.open_dataset('SST-V2.nc')
print("读取ERA5数据...")
era5_data = xr.open_dataset('data_ERA5.nc')

# 确认时间维度匹配
print(f"SST时间范围: {sst_data.time.values[0]} - {sst_data.time.values[-1]}, 长度: {len(sst_data.time)}")
print(f"ERA5时间范围: {era5_data.valid_time.values[0]} - {era5_data.valid_time.values[-1]}, 长度: {len(era5_data.valid_time)}")

# 将ERA5数据重命名时间维度，便于后续处理
era5_data = era5_data.rename({'valid_time': 'time'})

# 网格匹配：将ERA5数据（0.25°分辨率）插值到SST的网格（0.05°分辨率）
print("将ERA5数据插值到SST网格...")

def interpolate_era5_to_sst(era5_ds, sst_ds, var_name):
    """将ERA5变量插值到SST网格"""
    # 创建出原始经纬度网格点
    lon_era5, lat_era5 = np.meshgrid(era5_ds.longitude.values, era5_ds.latitude.values)
    
    # 创建目标经纬度网格点
    lon_sst, lat_sst = np.meshgrid(sst_ds.longitude.values, sst_ds.latitude.values)
    
    # 初始化结果数组
    result = np.zeros((len(era5_ds.time), len(sst_ds.latitude), len(sst_ds.longitude)))
    
    # 对每个时间点进行插值
    for t in range(len(era5_ds.time)):
        if t % 500 == 0:
            print(f"  处理时间点: {t}/{len(era5_ds.time)}")
        
        # 获取ERA5当前时间的数据
        z = era5_ds[var_name].isel(time=t).values
        
        # 将原始经纬度网格和数据平铺
        points = np.column_stack((lon_era5.flatten(), lat_era5.flatten()))
        values = z.flatten()
        
        # 使用griddata进行插值
        result[t] = griddata(points, values, (lon_sst, lat_sst), method='linear')
    
    # 创建xarray数据集
    result_da = xr.DataArray(
        result,
        dims=('time', 'latitude', 'longitude'),
        coords={
            'time': era5_ds.time.values,
            'latitude': sst_ds.latitude.values,
            'longitude': sst_ds.longitude.values
        }
    )
    return result_da

# 选择ERA5的2m气温进行插值
print("插值ERA5的2m气温...")
t2m_interpolated = interpolate_era5_to_sst(era5_data, sst_data, 't2m')

# 准备组合数据集
print("准备组合SST和插值后的ERA5气温数据...")
combined_ds = xr.Dataset(
    data_vars={
        'sst': sst_data.analysed_sst,
        't2m': t2m_interpolated
    },
    coords={
        'time': sst_data.time,
        'latitude': sst_data.latitude,
        'longitude': sst_data.longitude
    }
)

# 检查是否有缺失值
print(f"SST缺失值: {np.isnan(combined_ds.sst.values).sum()}")
print(f"t2m缺失值: {np.isnan(combined_ds.t2m.values).sum()}")

# 数据集划分：训练集、验证集和测试集（7:2:1）
print("划分数据集...")
total_time = len(combined_ds.time)
train_end = int(total_time * 0.7)
val_end = int(total_time * 0.9)

train_ds = combined_ds.isel(time=slice(0, train_end))
val_ds = combined_ds.isel(time=slice(train_end, val_end))
test_ds = combined_ds.isel(time=slice(val_end, None))

print(f"训练集: {train_ds.time.values[0]} - {train_ds.time.values[-1]}, 长度: {len(train_ds.time)}")
print(f"验证集: {val_ds.time.values[0]} - {val_ds.time.values[-1]}, 长度: {len(val_ds.time)}")
print(f"测试集: {test_ds.time.values[0]} - {test_ds.time.values[-1]}, 长度: {len(test_ds.time)}")

# 数据标准化
print("标准化数据...")

# 计算训练集的均值和标准差
train_mean_sst = train_ds.sst.mean(dim='time').values
train_std_sst = train_ds.sst.std(dim='time').values
train_mean_t2m = train_ds.t2m.mean(dim='time').values
train_std_t2m = train_ds.t2m.std(dim='time').values

# 对训练集标准化
train_sst_norm = (train_ds.sst.values - train_mean_sst) / train_std_sst
train_t2m_norm = (train_ds.t2m.values - train_mean_t2m) / train_std_t2m

# 对验证集标准化
val_sst_norm = (val_ds.sst.values - train_mean_sst) / train_std_sst
val_t2m_norm = (val_ds.t2m.values - train_mean_t2m) / train_std_t2m

# 对测试集标准化
test_sst_norm = (test_ds.sst.values - train_mean_sst) / train_std_sst
test_t2m_norm = (test_ds.t2m.values - train_mean_t2m) / train_std_t2m

# 保存归一化参数，用于后期反归一化
print("保存归一化参数...")
np.save('results/sst_mean.npy', train_mean_sst)
np.save('results/sst_std.npy', train_std_sst)
np.save('results/t2m_mean.npy', train_mean_t2m)
np.save('results/t2m_std.npy', train_std_t2m)

# 第二步：EOF分解
print("\n第二步：EOF分解")

# 准备用于EOF分解的数据（训练集+验证集）
print("准备训练集和验证集数据用于EOF分解...")
# 合并训练集和验证集的标准化数据
train_val_sst_norm = np.concatenate((train_sst_norm, val_sst_norm), axis=0)
train_val_t2m_norm = np.concatenate((train_t2m_norm, val_t2m_norm), axis=0)

# 重组数据为 (时间点, 变量数, lat, lon)
n_time_train_val = train_val_sst_norm.shape[0]
n_lat = train_val_sst_norm.shape[1]
n_lon = train_val_sst_norm.shape[2]

# 创建多变量数据数组
train_val_data = np.zeros((n_time_train_val, 2, n_lat, n_lon))
train_val_data[:, 0, :, :] = train_val_sst_norm  # SST
train_val_data[:, 1, :, :] = train_val_t2m_norm  # t2m

# 重组为EOF分解所需的格式：(空间点数*变量数, 时间点数)
X = train_val_data.reshape(n_time_train_val, -1).T  # reshape to (空间点数*变量数, 时间点数)

print(f"准备好的数据矩阵X形状: {X.shape}")

# 计算异常矩阵（减去时间均值）
X_mean = np.mean(X, axis=1, keepdims=True)
X_prime = X - X_mean

# 计算协方差矩阵
print("计算协方差矩阵...")
C = np.dot(X_prime, X_prime.T) / (n_time_train_val - 1)

# 特征值分解
print("进行特征值分解...")
eig_vals, eig_vecs = linalg.eigh(C)

# 按特征值大小降序排序
idx = np.argsort(eig_vals)[::-1]
eig_vals = eig_vals[idx]
eig_vecs = eig_vecs[:, idx]

# 计算方差贡献率和累积方差贡献率
var_exp = eig_vals / np.sum(eig_vals)
cum_var_exp = np.cumsum(var_exp)

# 选择前k=10个模态
k = 10
print(f"选择前{k}个EOF模态")
EOFs_k = eig_vecs[:, :k]
print(f"EOFs_k形状: {EOFs_k.shape}")

# 计算时间系数（PCs）
print("计算时间系数（PCs）...")
PCs_train_val = np.dot(EOFs_k.T, X_prime)
print(f"PCs_train_val形状: {PCs_train_val.shape}")

# 投影测试集
print("投影测试集到相同的EOFs上...")
# 重组测试集数据
test_data = np.zeros((len(test_ds.time), 2, n_lat, n_lon))
test_data[:, 0, :, :] = test_sst_norm
test_data[:, 1, :, :] = test_t2m_norm
X_test = test_data.reshape(len(test_ds.time), -1).T
X_test_prime = X_test - X_mean
PCs_test = np.dot(EOFs_k.T, X_test_prime)
print(f"PCs_test形状: {PCs_test.shape}")

# 保存EOF分解结果
print("保存EOF分解结果...")
np.save('results/EOFs_k.npy', EOFs_k)
np.save('results/X_mean.npy', X_mean)
np.save('results/PCs_train_val.npy', PCs_train_val)
np.save('results/PCs_test.npy', PCs_test)
np.save('results/eig_vals.npy', eig_vals)
np.save('results/var_exp.npy', var_exp)
np.save('results/cum_var_exp.npy', cum_var_exp)

# 画出前10个模态的方差贡献率和累积方差贡献率
plt.figure(figsize=(10, 6))
plt.bar(range(1, k+1), var_exp[:k] * 100, alpha=0.5, color='b', label='方差贡献率')
plt.step(range(1, k+1), cum_var_exp[:k] * 100, where='mid', color='r', label='累积方差贡献率')
plt.xlabel('模态')
plt.ylabel('方差贡献率 (%)')
plt.title('前10个EOF模态的方差贡献率和累积方差贡献率')
plt.xticks(range(1, k+1))
plt.legend()
plt.grid(True)
plt.savefig('figures/variance_explained.png', dpi=300, bbox_inches='tight')

# 可视化前3个空间模态
fig, axes = plt.subplots(3, 2, figsize=(14, 12))
titles = ['SST EOF1', 'T2m EOF1', 'SST EOF2', 'T2m EOF2', 'SST EOF3', 'T2m EOF3']

for i in range(3):
    # 获取第i个EOF模态
    eof_i = EOFs_k[:, i].reshape(2, n_lat, n_lon)
    
    # SST
    im1 = axes[i, 0].imshow(eof_i[0], cmap='coolwarm', 
                          extent=[combined_ds.longitude.values.min(), 
                                 combined_ds.longitude.values.max(),
                                 combined_ds.latitude.values.min(),
                                 combined_ds.latitude.values.max()])
    axes[i, 0].set_title(titles[i*2])
    axes[i, 0].set_xlabel('经度')
    axes[i, 0].set_ylabel('纬度')
    plt.colorbar(im1, ax=axes[i, 0])
    
    # T2m
    im2 = axes[i, 1].imshow(eof_i[1], cmap='coolwarm',
                          extent=[combined_ds.longitude.values.min(),
                                 combined_ds.longitude.values.max(),
                                 combined_ds.latitude.values.min(),
                                 combined_ds.latitude.values.max()])
    axes[i, 1].set_title(titles[i*2+1])
    axes[i, 1].set_xlabel('经度')
    axes[i, 1].set_ylabel('纬度')
    plt.colorbar(im2, ax=axes[i, 1])

plt.tight_layout()
plt.savefig('figures/top3_eofs.png', dpi=300, bbox_inches='tight')

# 可视化前3个时间系数（PCs）
plt.figure(figsize=(12, 8))
time_idx = np.arange(PCs_train_val.shape[1])
for i in range(3):
    plt.plot(time_idx, PCs_train_val[i], label=f'PC{i+1}')
plt.xlabel('时间索引')
plt.ylabel('时间系数值')
plt.title('前3个主成分的时间系数')
plt.legend()
plt.grid(True)
plt.savefig('figures/top3_pcs.png', dpi=300, bbox_inches='tight')

# 分割训练集和验证集的PCs
PCs_train = PCs_train_val[:, :train_end]
PCs_val = PCs_train_val[:, train_end:]

# 保存训练集、验证集和测试集的PCs
np.save('results/PCs_train.npy', PCs_train)
np.save('results/PCs_val.npy', PCs_val)
np.save('results/PCs_test.npy', PCs_test)

print("数据预处理和EOF分解完成！")

# 关闭文件
plt.close('all')
sst_data.close()
era5_data.close() 