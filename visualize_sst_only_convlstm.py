#!/usr/bin/env python3
"""
纯SST ConvLSTM结果可视化脚本
可视化纯SST模型的预测结果、误差分布和时间序列

作者: AI Assistant
日期: 2025-07-02
"""

import numpy as np
import matplotlib.pyplot as plt
from matplotlib import rcParams
rcParams['font.sans-serif'] = ['SimHei']  # 设置支持中文的字体
rcParams['axes.unicode_minus'] = False    # 修复负号显示
import xarray as xr
import os
from datetime import datetime, timedelta
from tqdm import tqdm

print("开始纯SST ConvLSTM结果可视化...")

# 创建结果文件夹
os.makedirs('sst_only_visualization', exist_ok=True)

# 加载标准化参数
print("加载标准化参数...")
sst_mean = np.load('sst_pcs_era5_data/sst_mean.npy')
sst_std = np.load('sst_pcs_era5_data/sst_std.npy')

print(f"SST标准化参数: mean={sst_mean:.3f}, std={sst_std:.3f}")

def denormalize_sst(sst_norm):
    """反标准化SST数据"""
    return sst_norm * sst_std + sst_mean

def kelvin_to_celsius(temp_k):
    """开尔文转摄氏度"""
    return temp_k - 273.15

def visualize_sst_only_results(model_name):
    """可视化指定纯SST模型的结果"""
    print(f"\n可视化 {model_name} 纯SST模型结果...")
    
    # 加载预测结果
    try:
        predictions = np.load(f'sst_only_results/test_predictions_{model_name}.npy')
        targets = np.load(f'sst_only_results/test_targets_{model_name}.npy')
    except FileNotFoundError:
        print(f"未找到 {model_name} 的预测结果文件，跳过可视化")
        return
    
    print(f"预测结果形状: {predictions.shape}")
    print(f"目标结果形状: {targets.shape}")
    
    # 反标准化为实际温度值
    predictions_denorm = denormalize_sst(predictions)
    targets_denorm = denormalize_sst(targets)
    
    # 转换为摄氏度
    predictions_celsius = kelvin_to_celsius(predictions_denorm)
    targets_celsius = kelvin_to_celsius(targets_denorm)
    
    # 计算误差
    error_celsius = predictions_celsius - targets_celsius
    
    print(f"SST预测范围: {predictions_celsius.min():.2f}°C - {predictions_celsius.max():.2f}°C")
    print(f"SST真实范围: {targets_celsius.min():.2f}°C - {targets_celsius.max():.2f}°C")
    print(f"误差范围: {error_celsius.min():.2f}°C - {error_celsius.max():.2f}°C")
    
    # 计算统计指标
    rmse = np.sqrt(np.mean(error_celsius**2))
    mae = np.mean(np.abs(error_celsius))
    
    print(f"RMSE: {rmse:.4f}°C")
    print(f"MAE: {mae:.4f}°C")
    
    # 保存摄氏度结果
    np.save(f'sst_only_visualization/sst_pred_celsius_{model_name}.npy', predictions_celsius)
    np.save(f'sst_only_visualization/sst_true_celsius_{model_name}.npy', targets_celsius)
    np.save(f'sst_only_visualization/sst_error_celsius_{model_name}.npy', error_celsius)
    
    # 保存统计信息
    error_stats = {
        'rmse': rmse,
        'mae': mae,
        'mean_error': np.mean(error_celsius),
        'std_error': np.std(error_celsius)
    }
    np.save(f'sst_only_visualization/error_stats_{model_name}.npy', error_stats)
    
    # 读取原始SST数据以获取坐标信息
    sst_data = xr.open_dataset('SST-V2.nc')
    
    # 可视化几个时间点的SST对比
    sample_indices = [0, len(predictions)//4, len(predictions)//2, 
                     3*len(predictions)//4, len(predictions)-1]
    
    for i, sample_idx in enumerate(sample_indices):
        if sample_idx >= len(predictions):
            continue
            
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        
        # 真实SST
        im1 = axes[0].imshow(targets_celsius[sample_idx, 0], cmap='coolwarm', 
                            vmin=20, vmax=32,
                            extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                   sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[0].set_title(f'真实SST (°C)\n样本 {sample_idx}')
        axes[0].set_xlabel('经度')
        axes[0].set_ylabel('纬度')
        plt.colorbar(im1, ax=axes[0])
        
        # 预测SST
        im2 = axes[1].imshow(predictions_celsius[sample_idx, 0], cmap='coolwarm', 
                            vmin=20, vmax=32,
                            extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                   sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[1].set_title(f'预测SST (°C)\n样本 {sample_idx} [纯SST模型]')
        axes[1].set_xlabel('经度')
        axes[1].set_ylabel('纬度')
        plt.colorbar(im2, ax=axes[1])
        
        # 误差
        im3 = axes[2].imshow(error_celsius[sample_idx, 0], cmap='RdBu_r', 
                            vmin=-2, vmax=2,
                            extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                                   sst_data.latitude.values.min(), sst_data.latitude.values.max()])
        axes[2].set_title(f'预测误差 (°C)\n样本 {sample_idx} [纯SST模型]')
        axes[2].set_xlabel('经度')
        axes[2].set_ylabel('纬度')
        plt.colorbar(im3, ax=axes[2])
        
        plt.tight_layout()
        plt.savefig(f'sst_only_visualization/sst_comparison_sample{sample_idx}_{model_name}.png', 
                   dpi=300, bbox_inches='tight')
        plt.close()
    
    # 绘制RMSE空间分布
    rmse_spatial = np.sqrt(np.mean(error_celsius**2, axis=0))
    
    plt.figure(figsize=(12, 8))
    im = plt.imshow(rmse_spatial[0], cmap='viridis',
                   extent=[sst_data.longitude.values.min(), sst_data.longitude.values.max(),
                          sst_data.latitude.values.min(), sst_data.latitude.values.max()])
    plt.title(f'{model_name} 纯SST模型 - RMSE空间分布 (°C)')
    plt.xlabel('经度')
    plt.ylabel('纬度')
    plt.colorbar(im, label='RMSE (°C)')
    plt.savefig(f'sst_only_visualization/rmse_map_{model_name}.png', 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    # 绘制误差时间序列
    spatial_mean_error = np.mean(error_celsius, axis=(1, 2, 3))
    spatial_rmse_time = np.sqrt(np.mean(error_celsius**2, axis=(1, 2, 3)))
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
    
    # 平均误差时间序列
    ax1.plot(spatial_mean_error, color='blue', alpha=0.7)
    ax1.set_title(f'{model_name} 纯SST模型 - 空间平均误差时间序列')
    ax1.set_ylabel('平均误差 (°C)')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    
    # RMSE时间序列
    ax2.plot(spatial_rmse_time, color='red', alpha=0.7)
    ax2.set_title(f'{model_name} 纯SST模型 - RMSE时间序列')
    ax2.set_xlabel('时间步')
    ax2.set_ylabel('RMSE (°C)')
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'sst_only_visualization/error_time_series_{model_name}.png', 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    # 绘制空间平均SST时间序列对比
    spatial_mean_pred = np.mean(predictions_celsius, axis=(1, 2, 3))
    spatial_mean_true = np.mean(targets_celsius, axis=(1, 2, 3))
    
    plt.figure(figsize=(12, 6))
    plt.plot(spatial_mean_true, label='真实SST', color='blue', alpha=0.7)
    plt.plot(spatial_mean_pred, label='预测SST (纯SST模型)', color='red', alpha=0.7)
    plt.title(f'{model_name} 纯SST模型 - 空间平均SST时间序列对比')
    plt.xlabel('时间步')
    plt.ylabel('SST (°C)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(f'sst_only_visualization/spatial_mean_timeseries_{model_name}.png', 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    # 绘制误差分布直方图
    plt.figure(figsize=(10, 6))
    plt.hist(error_celsius.flatten(), bins=50, alpha=0.7, color='skyblue', density=True)
    plt.axvline(0, color='red', linestyle='--', alpha=0.8)
    plt.xlabel('预测误差 (°C)')
    plt.ylabel('密度')
    plt.title(f'{model_name} 纯SST模型 - 误差分布')
    plt.grid(True, alpha=0.3)
    
    # 添加统计信息
    plt.text(0.05, 0.95, f'RMSE: {rmse:.4f}°C\nMAE: {mae:.4f}°C\n偏差: {np.mean(error_celsius):.4f}°C', 
             transform=plt.gca().transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.savefig(f'sst_only_visualization/error_distribution_{model_name}.png', 
               dpi=300, bbox_inches='tight')
    plt.close()
    
    # 创建NetCDF文件保存结果
    print(f"创建NetCDF文件保存 {model_name} 纯SST模型结果...")
    
    # 获取测试集对应的时间索引
    n_total = len(sst_data.time)
    test_start_idx = int(n_total * 0.9)
    seq_len = 14
    
    # 创建时间坐标
    base_time = sst_data.time.values[test_start_idx + seq_len]
    time_coords = [base_time + np.timedelta64(i, 'D') for i in range(len(predictions))]
    
    # 创建xarray数据集
    ds_result = xr.Dataset(
        data_vars={
            'sst_pred': (['time', 'pred_len', 'latitude', 'longitude'], predictions_celsius),
            'sst_true': (['time', 'pred_len', 'latitude', 'longitude'], targets_celsius),
            'sst_error': (['time', 'pred_len', 'latitude', 'longitude'], error_celsius)
        },
        coords={
            'time': time_coords,
            'pred_len': [1],
            'latitude': sst_data.latitude.values,
            'longitude': sst_data.longitude.values
        },
        attrs={
            'description': f'纯SST ConvLSTM {model_name} 预测结果',
            'model': f'{model_name}_sst_only',
            'experiment_type': 'baseline_study',
            'input_features': 'SST_only',
            'units': '摄氏度',
            'rmse': rmse,
            'mae': mae,
            'created': datetime.now().isoformat()
        }
    )
    
    # 保存NetCDF文件
    ds_result.to_netcdf(f'sst_only_visualization/sst_prediction_results_{model_name}.nc')
    
    sst_data.close()
    print(f"{model_name} 纯SST模型可视化完成！")

# 可视化纯SST模型结果
model_types = ["sst_only_basic"]

for model_name in model_types:
    visualize_sst_only_results(model_name)

print("\n纯SST ConvLSTM结果可视化完成！")
print("结果保存在 sst_only_visualization/ 文件夹中")

# 生成纯SST模型总结
print("\n生成纯SST模型总结...")

summary_text = f"""
# 纯SST ConvLSTM模型总结

## 模型特点
- **输入特征**: 仅SST历史序列
- **输入通道数**: 1个 (最简单的配置)
- **模型类型**: 基线模型

## 实验目的
作为最简单的基线模型，用于验证其他特征（PCs、ERA5）的真正贡献

## 模型优势
1. **计算效率最高**: 参数量最少，训练最快
2. **部署简单**: 只需SST数据，无需额外特征
3. **基线参考**: 为其他模型提供性能下限

## 预期性能
预期性能应该是所有模型中最低的，因为：
- 缺少大气强迫信息（ERA5）
- 缺少全局模态信息（PCs）
- 只能依赖SST的时空演化规律

## 科学意义
通过对比纯SST模型与其他模型的性能差异，可以量化：
- PCs特征的贡献 = (SST+PCs性能) - (纯SST性能)
- ERA5特征的贡献 = (SST+ERA5性能) - (纯SST性能)
- 多特征融合效果 = (SST+PCs+ERA5性能) - (纯SST性能)

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

with open('sst_only_visualization/sst_only_model_summary.md', 'w', encoding='utf-8') as f:
    f.write(summary_text)

print("纯SST模型总结已保存到 sst_only_visualization/sst_only_model_summary.md")
